{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-2 gap-4">
        <h3 class="text-2xl sm:text-3xl font-bold text-white font-display">Publikumsfrage</h3>
        <button id="refresh-question-button" class="action-button primary text-lg py-3 px-6 font-bold shadow-lg" title="Frage aktualisieren">
            <i class="fas fa-sync-alt mr-2"></i> Aktualisieren
        </button>
    </div>
    <div class="flex justify-between items-center mb-4">
        <div class="text-left">
            <p class="text-lg text-gray-300 mb-2">
                Aktueller Spieler: <span class="font-bold text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">{{ game_state.players[game_state.current_player].name }}</span>
            </p>
            <p class="text-lg text-gray-300">
                Deine <PERSON>: <span class="font-bold">{{ audience_member.score }}</span>
            </p>
        </div>
        <div class="text-right">
            <p class="text-lg text-gray-300 mb-2">
                Publikumspunkte: <span class="font-bold text-yellow-300">{{ game_state.audience_points[game_state.current_player] }}</span>
            </p>
            <p class="text-lg text-gray-300">
                Teilnehmer: <span class="font-bold">{{ game_state.audience_members|length }}</span>
            </p>
        </div>
    </div>
</div>

{% if current_question %}
<div class="question-container">
    <div id="question-display" class="animate-fade-in">
        <div class="question-text" id="question-text">
            {{ question.text }}
        </div>

        {% if question.question_image %}
        <div class="question-image mt-6 mb-6">
            <img src="{{ question.question_image }}" alt="Fragebild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('{{ question.question_image }}')"
                 style="max-height: 300px; cursor: pointer;">
        </div>
        {% endif %}

        <div class="answers-container">
            {% for answer in question.answers %}
            <button class="answer-button animate-slide-in" id="answer-{{ loop.index0 }}" data-index="{{ loop.index0 }}" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                <span class="answer-letter">{{ ['A', 'B', 'C'][loop.index0] }}</span>
                {{ answer }}
            </button>
            {% endfor %}
        </div>

        <div id="result-display" class="mt-8 text-center hidden">
            <div id="waiting-message" class="text-xl font-medium text-white mb-4">
                <p>Deine Antwort wurde gespeichert!</p>
                <p class="text-lg text-gray-300 mt-2">Warte, bis der Moderator die Antwort aufdeckt...</p>
                <div class="mt-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
            </div>
            <div id="correct-message" class="text-2xl font-bold text-correct mb-4 hidden">Richtig!</div>
            <div id="incorrect-message" class="text-2xl font-bold text-incorrect mb-4 hidden">Leider falsch!</div>
            <p id="correct-answer-text" class="text-lg text-white hidden">Die richtige Antwort ist: <span id="correct-answer" class="font-bold"></span></p>

            <div id="audience-stats" class="mt-6 p-4 rounded-lg bg-black bg-opacity-20 hidden">
                <h4 class="text-lg font-bold text-white mb-2">Publikumsstatistik</h4>
                <div id="audience-stats-content" class="text-white"></div>
            </div>

            <div id="explanation-section" class="explanation-section glass-container mt-6 p-4 rounded-xl hidden">
                <h4 class="text-lg font-bold text-white mb-2">Erklärung:</h4>
                <div id="explanation-content"></div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="text-center p-8 glass-container rounded-xl">
    <h4 class="text-2xl font-bold mb-4 text-white">Warte auf die nächste Frage...</h4>
    <p class="text-lg text-gray-300 mb-4">
        Sobald eine neue Frage gestellt wird, kannst du hier mitspielen.
    </p>

    <div class="animate-pulse mt-4 mb-6">
        <div class="inline-block rounded-full h-12 w-12 border-4 border-primary"></div>
    </div>

    <p class="text-sm text-gray-400 mb-6">
        Aktueller Spieler: <span class="font-bold text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">{{ game_state.players[game_state.current_player].name }}</span>
    </p>

    <div class="mt-4">
        <button id="refresh-button" class="action-button primary text-lg py-3 px-6 font-bold shadow-lg">Aktualisieren</button>
    </div>
</div>

<!-- Debug info -->
<div class="mt-8 p-4 bg-black bg-opacity-30 rounded text-xs text-gray-400 max-w-lg mx-auto">
    <p>Debug Info:</p>
    <p>Game Phase: {{ game_state.game_phase }}</p>
    <p>Used Categories: {{ game_state.used_categories|length }}</p>
    {% if game_state.used_categories %}
    <p>Last Category: {{ game_state.used_categories[-1] }}</p>
    {% endif %}
    <p>Current Question: {{ current_question }}</p>
    {% if category %}
    <p>Category: {{ category.name }}</p>
    {% endif %}
    {% if question %}
    <p>Question: {{ question.text|truncate(30) }}</p>
    {% endif %}
</div>
{% endif %}

<div class="mt-8 text-center">
    <a href="/audience" class="text-primary hover:underline">Zurück zur Übersicht</a>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the refresh button that's always visible
        const refreshQuestionButton = document.getElementById('refresh-question-button');
        if (refreshQuestionButton) {
            refreshQuestionButton.addEventListener('click', function() {
                window.location.reload();
            });
        }

        {% if current_question %}
        const answerButtons = document.querySelectorAll('.answer-button');
        const resultDisplay = document.getElementById('result-display');
        const waitingMessage = document.getElementById('waiting-message');
        const correctMessage = document.getElementById('correct-message');
        const incorrectMessage = document.getElementById('incorrect-message');
        const correctAnswerText = document.getElementById('correct-answer-text');
        const correctAnswer = document.getElementById('correct-answer');

        let selectedIndex = null;
        let hasAnswered = false;
        let questionRevealed = {{ 'true' if question_revealed else 'false' }};

        // If the question has been revealed, check if we have an answer stored
        if (questionRevealed) {
            checkAnswer();
        }

        // Add event listeners to answer buttons
        answerButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (!hasAnswered && !questionRevealed) {
                    // Remove active class from all buttons
                    answerButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to selected button
                    this.classList.add('active');

                    // Store selected index
                    selectedIndex = parseInt(this.dataset.index);

                    // Submit answer
                    submitAnswer(selectedIndex);
                }
            });
        });

        function submitAnswer(answerIndex) {
            if (hasAnswered) return;

            fetch('/api/audience-answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    category: '{{ current_question }}',
                    answer: answerIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hasAnswered = true;

                    // Store the answer in local storage
                    localStorage.setItem('audience_answer_' + '{{ current_question }}', answerIndex);

                    // Check if the answer was already processed (question already revealed)
                    if (data.correct !== undefined) {
                        // Question was already revealed, show result immediately
                        questionRevealed = true;

                        // Mark correct and incorrect answers
                        fetch('/api/current-question')
                        .then(response => response.json())
                        .then(questionData => {
                            if (questionData.success && questionData.question) {
                                const correctIndex = questionData.question.correct;

                                // Mark correct and incorrect answers
                                answerButtons.forEach((button, index) => {
                                    if (index === correctIndex) {
                                        button.classList.add('correct');
                                    } else if (index === answerIndex && index !== correctIndex) {
                                        button.classList.add('incorrect');
                                    }
                                });

                                // Show result message
                                if (data.correct) {
                                    correctMessage.classList.remove('hidden');
                                } else {
                                    incorrectMessage.classList.remove('hidden');
                                }

                                // Show correct answer
                                correctAnswer.textContent = document.querySelector(`#answer-${correctIndex}`).textContent.trim();
                                correctAnswerText.classList.remove('hidden');
                                resultDisplay.classList.remove('hidden');
                            }
                        });
                    } else {
                        // Show waiting message
                        waitingMessage.classList.remove('hidden');
                        resultDisplay.classList.remove('hidden');

                        // Start polling for question reveal
                        startPolling();
                    }

                    // Disable answer buttons
                    answerButtons.forEach(btn => {
                        btn.style.pointerEvents = 'none';
                        btn.classList.add('opacity-70');
                    });
                } else {
                    alert('Es gab ein Problem bei der Antwortübermittlung: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting answer:', error);
                alert('Es gab ein Problem bei der Antwortübermittlung. Bitte versuche es erneut.');
            });
        }

        function startPolling() {
            // Poll every 2 seconds to check if the question has been revealed
            let isReloading = false; // Flag to prevent multiple reloads

            let lastPollTime = 0;
            const POLL_COOLDOWN = 3000; // 3 seconds between polls

            const pollInterval = setInterval(function() {
                if (isReloading) return;

                // Implement a cooldown for polling too
                const now = Date.now();
                if (now - lastPollTime < POLL_COOLDOWN) {
                    return;
                }
                lastPollTime = now;

                fetch('/api/current-question')
                .then(response => response.json())
                .then(data => {
                    console.log('Polling response:', data);
                    // Check if the question has been revealed
                    if (data.success && data.question_revealed) {
                        // Question has been revealed, stop polling
                        clearInterval(pollInterval);
                        questionRevealed = true;

                        // Check our answer
                        checkAnswer();
                    }
                    // Also check if the question has changed
                    else if (data.success && data.current_question && data.current_question !== '{{ current_question }}') {
                        console.log('Question has changed, reloading page...');
                        isReloading = true;

                        // Store the fact that we're reloading in sessionStorage
                        sessionStorage.setItem('audience_reloading', 'true');
                        sessionStorage.setItem('audience_last_reload', Date.now().toString());

                        // Add a longer delay before reloading
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Error polling for question reveal:', error);
                });
            }, 3000); // Poll every 3 seconds (increased from 2)
        }

        function fetchExplanation() {
            fetch('/api/current-question')
            .then(response => response.json())
            .then(data => {
                if (data.success && (data.explanation || data.image)) {
                    const explanationSection = document.getElementById('explanation-section');
                    const explanationContent = document.getElementById('explanation-content');

                    let contentHTML = '';

                    if (data.image) {
                        contentHTML += `
                            <div class="explanation-image mb-4">
                                <img src="${data.image}" alt="Erklärungsbild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('${data.image}')">
                            </div>
                        `;
                    }

                    if (data.explanation) {
                        contentHTML += `
                            <div class="explanation-text text-white">
                                ${data.explanation}
                            </div>
                        `;
                    }

                    explanationContent.innerHTML = contentHTML;
                    explanationSection.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error fetching explanation:', error);
            });
        }

        function fetchAudienceStats() {
            // Fetch audience statistics for this question
            fetch('/api/current-question')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.audience_results) {
                    const audienceStats = document.getElementById('audience-stats');
                    const audienceStatsContent = document.getElementById('audience-stats-content');
                    const results = data.audience_results;

                    if (results.total_answers > 0) {
                        const correctAnswers = results.correct_answers;
                        const totalAnswers = results.total_answers;
                        const correctPercentage = Math.round((correctAnswers / totalAnswers) * 100);
                        const answerCounts = results.answer_counts;

                        // Calculate percentages for each answer
                        const percentA = totalAnswers > 0 ? Math.round((answerCounts[0] / totalAnswers) * 100) : 0;
                        const percentB = totalAnswers > 0 ? Math.round((answerCounts[1] / totalAnswers) * 100) : 0;
                        const percentC = totalAnswers > 0 ? Math.round((answerCounts[2] / totalAnswers) * 100) : 0;

                        audienceStatsContent.innerHTML = `
                            <div class="flex justify-between items-center mb-3">
                                <div class="text-white text-left">
                                    <div><span class="font-bold">${totalAnswers}</span> Teilnehmer</div>
                                    <div><span class="font-bold">${correctAnswers}</span> richtige Antworten</div>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-green-400">${correctPercentage}% Erfolgsquote</div>
                                </div>
                            </div>

                            <div class="text-sm mt-2 mb-2">Antwortverteilung:</div>
                            <div class="flex flex-col space-y-2">
                                <div class="flex items-center">
                                    <span class="answer-letter mr-2">A</span>
                                    <div class="bg-gray-700 h-5 flex-grow rounded-full overflow-hidden">
                                        <div class="bg-blue-500 h-full rounded-full" style="width: ${percentA}%"></div>
                                    </div>
                                    <span class="ml-2">${percentA}%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="answer-letter mr-2">B</span>
                                    <div class="bg-gray-700 h-5 flex-grow rounded-full overflow-hidden">
                                        <div class="bg-blue-500 h-full rounded-full" style="width: ${percentB}%"></div>
                                    </div>
                                    <span class="ml-2">${percentB}%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="answer-letter mr-2">C</span>
                                    <div class="bg-gray-700 h-5 flex-grow rounded-full overflow-hidden">
                                        <div class="bg-blue-500 h-full rounded-full" style="width: ${percentC}%"></div>
                                    </div>
                                    <span class="ml-2">${percentC}%</span>
                                </div>
                            </div>
                        `;

                        audienceStats.classList.remove('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching audience stats:', error);
            });
        }

        function checkAnswer() {
            // Get our stored answer
            const storedAnswer = localStorage.getItem('audience_answer_' + '{{ current_question }}');
            if (!storedAnswer) {
                console.log('No stored answer found for', '{{ current_question }}');
                return;
            }

            console.log('Checking answer:', storedAnswer, 'for question', '{{ current_question }}');
            const answerIndex = parseInt(storedAnswer);

            // Get the correct answer from the server
            fetch('/api/current-question')
            .then(response => response.json())
            .then(data => {
                console.log('Check answer response:', data);
                if (data.success && data.question && data.question_revealed) {
                    const correctIndex = data.question.correct;
                    console.log('Correct index:', correctIndex, 'Our answer:', answerIndex);

                    // Hide waiting message
                    waitingMessage.classList.add('hidden');

                    // Mark correct and incorrect answers
                    answerButtons.forEach((button, index) => {
                        if (index === correctIndex) {
                            button.classList.add('correct');
                        } else if (index === answerIndex && index !== correctIndex) {
                            button.classList.add('incorrect');
                        }
                    });

                    // Show result message
                    if (answerIndex === correctIndex) {
                        correctMessage.classList.remove('hidden');
                    } else {
                        incorrectMessage.classList.remove('hidden');
                    }

                    // Show correct answer
                    correctAnswer.textContent = document.querySelector(`#answer-${correctIndex}`).textContent.trim();
                    correctAnswerText.classList.remove('hidden');
                    resultDisplay.classList.remove('hidden');

                    // Fetch audience statistics and explanation
                    fetchAudienceStats();
                    fetchExplanation();

                    // Auto-refresh after 12 seconds (increased to give time to view stats and explanation)
                    setTimeout(function() {
                        window.location.reload();
                    }, 12000);
                }
            })
            .catch(error => {
                console.error('Error checking answer:', error);
            });
        }
        {% else %}
        // Add event listener to refresh button
        document.getElementById('refresh-button').addEventListener('click', function() {
            window.location.reload();
        });

        // Check for new questions with improved refresh mechanism
        let isReloading = false; // Flag to prevent multiple reloads
        let lastQuestionId = null; // Track the last question ID to avoid unnecessary reloads
        let lastCheckTime = 0; // Track the last time we checked to implement a cooldown
        const RELOAD_COOLDOWN = 5000; // 5 seconds cooldown between reloads

        // Function to check for new questions
        function checkForNewQuestions() {
            // Don't check if we're already reloading
            if (isReloading) return;

            // Implement a cooldown to prevent too frequent reloads
            const now = Date.now();
            if (now - lastCheckTime < RELOAD_COOLDOWN) {
                console.log('Skipping check due to cooldown');
                return;
            }

            lastCheckTime = now;
            console.log('Checking for new questions...');

            fetch('/api/current-question')
            .then(response => response.json())
            .then(data => {
                console.log('Current question check:', data);

                // Only reload if there's a question available AND it's different from the last one we saw
                if (data.success && data.current_question && data.current_question !== lastQuestionId) {
                    console.log('New question available, reloading page...');
                    lastQuestionId = data.current_question;
                    isReloading = true;

                    // Store the fact that we're reloading in sessionStorage to prevent reload loops
                    sessionStorage.setItem('audience_reloading', 'true');
                    sessionStorage.setItem('audience_last_reload', now.toString());

                    // Add a longer delay before reloading to prevent rapid successive reloads
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else if (data.success && data.current_question) {
                    // We have a question but it's the same as before
                    lastQuestionId = data.current_question;
                }
            })
            .catch(error => {
                console.error('Error checking for new questions:', error);
            });
        }

        // Check if we recently reloaded to prevent reload loops
        const lastReload = parseInt(sessionStorage.getItem('audience_last_reload') || '0');
        const now = Date.now();

        if (now - lastReload < RELOAD_COOLDOWN) {
            console.log('Recently reloaded, waiting before checking again...');
            // Clear the reloading flag after a delay
            setTimeout(function() {
                sessionStorage.removeItem('audience_reloading');
                // Then do an initial check
                checkForNewQuestions();
            }, RELOAD_COOLDOWN);
        } else {
            // Initial check after a short delay to ensure session is established
            setTimeout(checkForNewQuestions, 1000);
            sessionStorage.removeItem('audience_reloading');
        }

        // Then check every 3 seconds (increased from 2 seconds)
        const refreshInterval = setInterval(checkForNewQuestions, 3000);
        {% endif %}
    });
</script>
{% endblock %}
