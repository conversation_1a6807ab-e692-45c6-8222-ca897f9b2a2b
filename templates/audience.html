{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-8">
    <div class="flex justify-between items-center mb-2">
        <h3 class="text-5xl font-bold text-white bg-gradient-to-r from-cyan-500 to-magenta-500 inline-block text-transparent bg-clip-text">Publikumsteilnahme</h3>
        <button id="refresh-button" class="action-button primary text-sm py-2 px-4" title="Seite aktualisieren">
            <i class="fas fa-sync-alt mr-1"></i> Aktualisieren
        </button>
    </div>
    <p class="text-xl text-white mb-6">
        Spiele mit und sammle Punkte für den aktuellen Spieler!
    </p>
</div>

<div class="flex justify-center">
    <div class="glass-container p-8 rounded-xl text-center max-w-lg">
        <h4 class="text-2xl font-bold mb-4 text-white">Wie es funktioniert:</h4>
        <ul class="text-left text-white space-y-3 mb-6">
            <li><i class="fas fa-check-circle text-primary mr-2"></i> Du bekommst die gleichen Fragen wie die Spieler</li>
            <li><i class="fas fa-check-circle text-primary mr-2"></i> Deine Punkte gehen an den Spieler, der gerade an der Reihe ist</li>
            <li><i class="fas fa-check-circle text-primary mr-2"></i> Für jede richtige Antwort gibt es 25 Punkte</li>
            <li><i class="fas fa-check-circle text-primary mr-2"></i> Die Antwort wird erst aufgedeckt, wenn der Moderator sie aufdeckt</li>
        </ul>

        <div class="mt-6">
            <a href="/audience/question" id="start-button" class="action-button primary">Jetzt mitspielen</a>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the refresh button
        const refreshButton = document.getElementById('refresh-button');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                window.location.reload();
            });
        }

        const startButton = document.getElementById('start-button');
        let isRedirecting = false;

        // Check if user has already registered
        const hasRegistered = localStorage.getItem('audience_registered');

        // Function to check for active questions with debouncing
        let lastCheckTime = 0;
        const CHECK_COOLDOWN = 3000; // 3 seconds between checks

        function checkForActiveQuestion() {
            if (isRedirecting) return;

            // Implement a cooldown to prevent too frequent checks
            const now = Date.now();
            if (now - lastCheckTime < CHECK_COOLDOWN) {
                console.log('Skipping active question check due to cooldown');
                return;
            }
            lastCheckTime = now;

            fetch('/api/current-question')
            .then(response => response.json())
            .then(data => {
                console.log('Checking for active question:', data);

                // Check if we're in the master phase
                if (data.success && data.game_phase === 'master') {
                    console.log('Master question active, redirecting to master question page');
                    isRedirecting = true;

                    // Store the fact that we're redirecting to prevent loops
                    sessionStorage.setItem('audience_redirecting', 'true');
                    sessionStorage.setItem('audience_last_redirect', Date.now().toString());

                    // Add a small delay before redirecting
                    setTimeout(function() {
                        window.location.href = '/audience/master';
                    }, 500);
                }
                // Check if there's an active regular question
                else if (data.success && data.current_question) {
                    console.log('Active question found, redirecting to question page');
                    isRedirecting = true;

                    // Store the fact that we're redirecting to prevent loops
                    sessionStorage.setItem('audience_redirecting', 'true');
                    sessionStorage.setItem('audience_last_redirect', Date.now().toString());

                    // Add a small delay before redirecting
                    setTimeout(function() {
                        window.location.href = '/audience/question';
                    }, 500);
                }
                // Check if the game is finished
                else if (data.success && data.game_phase === 'finished') {
                    console.log('Game finished, redirecting to results page');
                    isRedirecting = true;

                    // Store the fact that we're redirecting to prevent loops
                    sessionStorage.setItem('audience_redirecting', 'true');
                    sessionStorage.setItem('audience_last_redirect', Date.now().toString());

                    // Add a small delay before redirecting
                    setTimeout(function() {
                        window.location.href = '/audience/results';
                    }, 500);
                }
            })
            .catch(error => {
                console.error('Error checking for active question:', error);
            });
        }

        // If already registered, check if we need to redirect
        if (hasRegistered === 'true') {
            console.log('User already registered, checking session...');

            // Check if we're already on the question page to avoid redirect loops
            if (window.location.pathname !== '/audience/question') {
                console.log('Not on question page, checking session validity...');

                // Set a flag in sessionStorage to prevent multiple redirects
                if (sessionStorage.getItem('redirecting') !== 'true') {
                    sessionStorage.setItem('redirecting', 'true');

                    // Check if there's a valid session on the server
                    fetch('/api/debug')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Debug response:', data);
                        sessionStorage.removeItem('redirecting'); // Clear the flag

                        if (data.success && data.session_id !== 'not_set') {
                            console.log('Valid session found, checking for active question');
                            checkForActiveQuestion();

                            // Start polling for active questions
                            setInterval(checkForActiveQuestion, 2000);
                        } else {
                            console.log('No valid session found, re-registering');
                            // Clear local storage and stay on this page to re-register
                            localStorage.removeItem('audience_registered');
                        }
                    })
                    .catch(error => {
                        console.error('Error checking session:', error);
                        sessionStorage.removeItem('redirecting'); // Clear the flag
                        // On error, assume we need to re-register
                        localStorage.removeItem('audience_registered');
                    });
                } else {
                    console.log('Already redirecting, preventing redirect loop');
                    sessionStorage.removeItem('redirecting'); // Clear the flag to allow future redirects
                }
            } else {
                console.log('Already on question page, no redirect needed');
            }
            return;
        }

        // Add event listener to start button
        startButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Start button clicked');

            // Disable the button immediately to prevent multiple clicks
            startButton.disabled = true;
            startButton.classList.add('opacity-50');
            startButton.textContent = 'Registriere...';

            // Submit registration to API
            fetch('/api/audience-register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log('Registration response:', data);
                if (data.success) {
                    // Store registration in local storage
                    localStorage.setItem('audience_registered', 'true');

                    // Clear any previous reload flags
                    sessionStorage.removeItem('audience_reloading');

                    // Add a delay before checking for questions to ensure session is established
                    startButton.textContent = 'Verbindung wird hergestellt...';

                    setTimeout(function() {
                        // Verify session is established with a second request
                        fetch('/api/debug')
                        .then(response => response.json())
                        .then(debugData => {
                            console.log('Session verification:', debugData);

                            if (debugData.success && debugData.session_id !== 'not_set') {
                                // Session is established, now check for active questions
                                startButton.textContent = 'Prüfe auf aktive Fragen...';

                                // Check if there's an active question
                                checkForActiveQuestion();

                                // If no active question, just show a message
                                if (!isRedirecting) {
                                    startButton.textContent = 'Warte auf Fragen...';

                                    // Start polling for active questions with a longer interval
                                    setInterval(checkForActiveQuestion, 3000);
                                }
                            } else {
                                // Session not established, show error
                                startButton.textContent = 'Fehler bei der Verbindung';
                                setTimeout(function() {
                                    startButton.textContent = 'Jetzt mitspielen';
                                    startButton.disabled = false;
                                    startButton.classList.remove('opacity-50');
                                }, 2000);
                            }
                        })
                        .catch(error => {
                            console.error('Error verifying session:', error);
                            startButton.textContent = 'Fehler bei der Verbindung';
                            setTimeout(function() {
                                startButton.textContent = 'Jetzt mitspielen';
                                startButton.disabled = false;
                                startButton.classList.remove('opacity-50');
                            }, 2000);
                        });
                    }, 1000); // Wait 1 second before verifying session
                } else {
                    alert('Es gab ein Problem bei der Registrierung. Bitte versuche es erneut.');
                    startButton.textContent = 'Jetzt mitspielen';
                    startButton.disabled = false;
                    startButton.classList.remove('opacity-50');
                }
            })
            .catch(error => {
                console.error('Error registering:', error);
                alert('Es gab ein Problem bei der Registrierung. Bitte versuche es erneut.');
                startButton.textContent = 'Jetzt mitspielen';
                startButton.disabled = false;
                startButton.classList.remove('opacity-50');
            });
        });
    });
</script>
{% endblock %}
