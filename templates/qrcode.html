{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-8">
    <h3 class="text-5xl font-bold mb-4 text-white bg-gradient-to-r from-cyan-500 to-magenta-500 inline-block text-transparent bg-clip-text">Publikumsteilnahme</h3>
    <p class="text-xl text-white mb-6">
        Scanne den QR-Code, um aktiv am Spiel teilzunehmen und Punkte für den aktuellen Spieler zu sammeln!
    </p>
</div>

<div class="mb-8 p-6 glass-container rounded-xl max-w-lg mx-auto">
    <h4 class="text-2xl font-bold mb-4 text-white">So funktioniert's:</h4>
    <ul class="text-left text-white space-y-3">
        <li><i class="fas fa-check-circle text-primary mr-2"></i> Beantworte die gleichen Fragen wie die Spieler</li>
        <li><i class="fas fa-check-circle text-primary mr-2"></i> Deine <PERSON> g<PERSON> an <PERSON>ler, der gerade an der Reihe ist</li>
        <li><i class="fas fa-check-circle text-primary mr-2"></i> Für jede richtige Antwort gibt es 100 Punkte</li>
        <li><i class="fas fa-check-circle text-primary mr-2"></i> Die Antwort wird erst aufgedeckt, wenn der Moderator sie aufdeckt</li>
    </ul>
</div>

<div class="flex flex-col items-center">
    <div class="qr-code-container bg-white p-4 rounded-lg mb-6">
        <img src="{{ qr_code }}" alt="QR Code für Publikumsteilnahme" class="w-64 h-64">
    </div>

    <p class="text-2xl text-white mb-4">oder besuche:</p>
    <a href="{{ audience_url }}" target="_blank" class="text-2xl font-bold mb-4 text-yellow-300 hover:text-yellow-100 transition-colors duration-300">{{ audience_url }}</a>

    <div class="mt-8 p-6 glass-container rounded-xl">
        <h4 class="text-2xl font-bold mb-4 text-white bg-gradient-to-r from-cyan-500 to-blue-500 inline-block text-transparent bg-clip-text">Publikumspunkte</h4>
        <div class="flex justify-center space-x-12">
            <div class="text-center">
                <p class="text-lg text-player1 mb-2">{{ game_state.players[0].name }}</p>
                <p class="text-3xl font-bold text-white">{{ game_state.audience_points[0] }}</p>
                {% set player1_count = 0 %}
                {% for member in game_state.audience_members.values() %}
                    {% if member.player == 0 %}
                        {% set player1_count = player1_count + 1 %}
                    {% endif %}
                {% endfor %}
                <p class="text-sm text-gray-300 mt-2">{{ player1_count }} Mitspieler</p>
            </div>
            <div class="text-center">
                <p class="text-lg text-player2 mb-2">{{ game_state.players[1].name }}</p>
                <p class="text-3xl font-bold text-white">{{ game_state.audience_points[1] }}</p>
                {% set player2_count = 0 %}
                {% for member in game_state.audience_members.values() %}
                    {% if member.player == 1 %}
                        {% set player2_count = player2_count + 1 %}
                    {% endif %}
                {% endfor %}
                <p class="text-sm text-gray-300 mt-2">{{ player2_count }} Mitspieler</p>
            </div>
        </div>
    </div>

    <div class="mt-6">
        <a href="/" class="action-button">Zurück zum Spiel</a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Manual refresh button
    document.addEventListener('DOMContentLoaded', function() {
        const refreshButton = document.querySelector('.action-button');
        if (refreshButton) {
            refreshButton.addEventListener('click', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    window.location.reload();
                }
            });
        }
    });
</script>
{% endblock %}
