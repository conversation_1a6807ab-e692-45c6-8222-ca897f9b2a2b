{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-8">
    <h3 class="text-4xl font-bold mb-4 text-white font-display">Endergebnis</h3>

    {% if winner is not none %}
    <div class="mb-6">
        <p class="text-2xl font-medium mb-2 text-white">
            <span class="font-bold text-{{ 'player1' if winner == 0 else 'player2' }}">{{ players[winner].name }}</span> hat gewonnen!
        </p>
        <p class="text-lg text-gray-300">
            Herzlichen Glückwunsch!
        </p>
    </div>
    {% else %}
    <div class="mb-6">
        <p class="text-2xl font-medium mb-2 text-white">
            Unentschieden!
        </p>
        <p class="text-lg text-gray-300">
            Beide Spieler haben die gleiche Punktzahl erreicht.
        </p>
    </div>
    {% endif %}
</div>

<div class="flex justify-center items-center space-x-8 mb-12">
    <div class="glass-container p-6 rounded-xl text-center w-64 {{ 'border-4 border-player1 animate-pulse' if winner == 0 }}">
        <div class="text-4xl text-player1 mb-2">
            <i class="fas fa-user"></i>
        </div>
        <h4 class="text-xl font-semibold mb-2">{{ players[0].name }}</h4>
        <div class="mb-2">
            <p class="text-3xl font-bold text-player1">{{ players[0].score + game_state.audience_points[0] }} Punkte</p>
        </div>
        <div class="flex justify-between text-sm text-gray-300 border-t border-gray-700 pt-2">
            <span>Spieler:</span>
            <span class="font-medium">{{ players[0].score }}</span>
        </div>
        <div class="flex justify-between text-sm text-gray-300">
            <span>Publikum:</span>
            <span class="font-medium">{{ game_state.audience_points[0] }}</span>
        </div>
    </div>

    <div class="glass-container p-6 rounded-xl text-center w-64 {{ 'border-4 border-player2 animate-pulse' if winner == 1 }}">
        <div class="text-4xl text-player2 mb-2">
            <i class="fas fa-user"></i>
        </div>
        <h4 class="text-xl font-semibold mb-2">{{ players[1].name }}</h4>
        <div class="mb-2">
            <p class="text-3xl font-bold text-player2">{{ players[1].score + game_state.audience_points[1] }} Punkte</p>
        </div>
        <div class="flex justify-between text-sm text-gray-300 border-t border-gray-700 pt-2">
            <span>Spieler:</span>
            <span class="font-medium">{{ players[1].score }}</span>
        </div>
        <div class="flex justify-between text-sm text-gray-300">
            <span>Publikum:</span>
            <span class="font-medium">{{ game_state.audience_points[1] }}</span>
        </div>
    </div>
</div>

<!-- Master Question Explanation Section -->
<div id="explanation-container" class="hidden mt-8 mb-12">
    <h4 class="text-2xl font-bold mb-4 text-center text-white">Erklärung zur Masterfrage</h4>
    <div class="explanation-section glass-container p-6 rounded-xl mx-auto max-w-3xl">
        <div id="explanation-content"></div>
    </div>
</div>

<!-- Audience Participation Section -->
<div class="mt-8 mb-12">
    <h4 class="text-2xl font-bold mb-6 text-center text-white">Publikumsteilnahme</h4>
    <div class="flex justify-center items-center space-x-8">
        <div class="glass-container p-4 rounded-xl text-center w-48">
            <h5 class="text-lg font-semibold mb-2 text-player1">{{ players[0].name }}</h5>
            <p class="text-2xl font-bold text-white">{{ game_state.audience_points[0] }} Punkte</p>
            {% set player1_correct = 0 %}
            {% for member in game_state.audience_members.values() %}
                {% if member.current_answer is not none and member.current_question is not none %}
                    {% set player1_correct = player1_correct + 1 %}
                {% endif %}
            {% endfor %}
            <p class="text-sm text-gray-300 mt-2">{{ player1_correct }} richtige Antworten</p>
        </div>

        <div class="glass-container p-4 rounded-xl text-center w-48">
            <h5 class="text-lg font-semibold mb-2 text-player2">{{ players[1].name }}</h5>
            <p class="text-2xl font-bold text-white">{{ game_state.audience_points[1] }} Punkte</p>
            {% set total_audience = game_state.audience_members|length %}
            <p class="text-sm text-gray-300 mt-2">{{ total_audience }} Teilnehmer</p>
        </div>
    </div>

    <!-- Master Question Audience Participation -->
    {% if game_state.audience_master_bets|length > 0 %}
    <div class="mt-8">
        <h5 class="text-xl font-bold mb-4 text-center text-white">Masterfrage - Publikumsbeteiligung</h5>
        <div class="flex justify-center items-center space-x-8">
            <div class="glass-container p-4 rounded-xl text-center w-48">
                <h5 class="text-lg font-semibold mb-2 text-player1">{{ players[0].name }}</h5>
                {% set player1_audience_count = 0 %}
                {% set player1_audience_points = 0 %}
                {% for audience_id, bet_data in game_state.audience_master_bets.items() %}
                    {% if bet_data.player == 0 %}
                        {% set player1_audience_count = player1_audience_count + 1 %}
                        {% set player1_audience_points = player1_audience_points + bet_data.bet %}
                    {% endif %}
                {% endfor %}
                <p class="text-lg font-bold text-white">{{ player1_audience_count }} Teilnehmer</p>
                <p class="text-lg font-bold text-player1">{{ player1_audience_points }} Punkte gesetzt</p>
            </div>

            <div class="glass-container p-4 rounded-xl text-center w-48">
                <h5 class="text-lg font-semibold mb-2 text-player2">{{ players[1].name }}</h5>
                {% set player2_audience_count = 0 %}
                {% set player2_audience_points = 0 %}
                {% for audience_id, bet_data in game_state.audience_master_bets.items() %}
                    {% if bet_data.player == 1 %}
                        {% set player2_audience_count = player2_audience_count + 1 %}
                        {% set player2_audience_points = player2_audience_points + bet_data.bet %}
                    {% endif %}
                {% endfor %}
                <p class="text-lg font-bold text-white">{{ player2_audience_count }} Teilnehmer</p>
                <p class="text-lg font-bold text-player2">{{ player2_audience_points }} Punkte gesetzt</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<div class="flex justify-center">
    <a href="/reset" class="action-button">Neues Spiel starten</a>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confetti effect for the winner
        {% if winner is not none %}
        createConfetti();
        {% endif %}

        // Fetch master question explanation
        fetchMasterExplanation();

        function createConfetti() {
            const confettiCount = 200;
            const container = document.querySelector('body');

            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';

                // Random position, color, and delay
                const size = Math.random() * 10 + 5;
                const left = Math.random() * 100;

                // Use winner's color for confetti
                {% if winner == 0 %}
                const colors = ['#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe'];
                {% else %}
                const colors = ['#ec4899', '#f472b6', '#f9a8d4', '#fbcfe8', '#fce7f3'];
                {% endif %}

                const color = colors[Math.floor(Math.random() * colors.length)];
                const delay = Math.random() * 5;

                confetti.style.width = `${size}px`;
                confetti.style.height = `${size}px`;
                confetti.style.left = `${left}%`;
                confetti.style.backgroundColor = color;
                confetti.style.animationDelay = `${delay}s`;

                container.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    confetti.remove();
                }, 5000 + (delay * 1000));
            }
        }

        function fetchMasterExplanation() {
            // Make an API call to get the master question explanation
            fetch('/api/master-reveal', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.explanation || data.image) {
                    const explanationContainer = document.getElementById('explanation-container');
                    const explanationContent = document.getElementById('explanation-content');

                    let contentHTML = '';

                    if (data.image) {
                        contentHTML += `
                            <div class="explanation-image mb-4">
                                <img src="${data.image}" alt="Erklärungsbild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('${data.image}')">
                            </div>
                        `;
                    }

                    if (data.explanation) {
                        contentHTML += `
                            <div class="explanation-text text-white">
                                ${data.explanation}
                            </div>
                        `;
                    }

                    explanationContent.innerHTML = contentHTML;
                    explanationContainer.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error fetching master explanation:', error);
            });
        }
    });
</script>
{% endblock %}
