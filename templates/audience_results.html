{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-8">
    <h3 class="text-4xl font-bold mb-4 text-white bg-gradient-to-r from-cyan-500 to-magenta-500 inline-block text-transparent bg-clip-text">Ergebnisse</h3>
    <p class="text-xl text-white mb-6">
        Vielen Dank für deine Teilnahme!
    </p>
</div>

<div class="flex flex-col items-center">
    <div class="glass-container p-8 rounded-xl text-center max-w-lg mb-8">
        <h4 class="text-2xl font-bold mb-4 text-white">Deine Statistik</h4>
        <p class="text-lg text-white mb-2">Du hast <span class="font-bold text-yellow-300">{{ audience_member.score }}</span> Punkte gesammelt!</p>

        {% if audience_id in game_state.audience_master_bets %}
            {% set bet_data = game_state.audience_master_bets[audience_id] %}
            {% set player_index = bet_data.player %}
            {% set bet_amount = bet_data.bet %}
            {% set answer_index = game_state.audience_master_answers[audience_id] %}

            <div class="mt-4 p-4 bg-opacity-20 bg-gray-700 rounded-lg">
                <h5 class="text-xl font-bold mb-2 text-white">Masterfrage</h5>
                <p class="text-gray-300">
                    Du hast <span class="font-bold text-{{ 'player1' if player_index == 0 else 'player2' }}">{{ bet_amount }}</span> Punkte
                    auf <span class="font-bold text-{{ 'player1' if player_index == 0 else 'player2' }}">{{ game_state.players[player_index].name }}</span> gesetzt.
                </p>

                {% set master_data = {'question': {'answers': ['Etwa 150.000', 'Etwa 300.000', 'Etwa 450.000']}} %}
                <p class="text-gray-300 mt-2">
                    Deine Antwort: <span class="font-bold">{{ ['A', 'B', 'C'][answer_index] }}</span> ({{ master_data.question.answers[answer_index] }})
                </p>
            </div>
        {% endif %}
    </div>

    <div class="glass-container p-8 rounded-xl text-center max-w-lg">
        <h4 class="text-2xl font-bold mb-4 text-white">Endergebnis</h4>

        <div class="grid grid-cols-2 gap-8">
            <!-- Player 1 -->
            <div class="flex flex-col items-center {% if winner == 0 %}relative{% endif %}">
                {% if winner == 0 %}
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <i class="fas fa-crown text-yellow-400 text-3xl"></i>
                </div>
                {% endif %}

                <img src="{{ players[0].image }}" alt="{{ players[0].name }}" class="w-16 h-16 rounded-full mb-2">
                <h5 class="text-xl font-bold text-player1">{{ players[0].name }}</h5>

                <div class="mt-2">
                    <p class="text-white">Spieler: <span class="font-bold">{{ players[0].score }}</span></p>
                    <p class="text-white">Publikum: <span class="font-bold">{{ game_state.audience_points[0] }}</span></p>
                    <p class="text-white text-lg mt-1">Gesamt: <span class="font-bold text-player1">{{ players[0].score + game_state.audience_points[0] }}</span></p>
                </div>
            </div>

            <!-- Player 2 -->
            <div class="flex flex-col items-center {% if winner == 1 %}relative{% endif %}">
                {% if winner == 1 %}
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <i class="fas fa-crown text-yellow-400 text-3xl"></i>
                </div>
                {% endif %}

                <img src="{{ players[1].image }}" alt="{{ players[1].name }}" class="w-16 h-16 rounded-full mb-2">
                <h5 class="text-xl font-bold text-player2">{{ players[1].name }}</h5>

                <div class="mt-2">
                    <p class="text-white">Spieler: <span class="font-bold">{{ players[1].score }}</span></p>
                    <p class="text-white">Publikum: <span class="font-bold">{{ game_state.audience_points[1] }}</span></p>
                    <p class="text-white text-lg mt-1">Gesamt: <span class="font-bold text-player2">{{ players[1].score + game_state.audience_points[1] }}</span></p>
                </div>
            </div>
        </div>

        {% if winner is none %}
        <div class="mt-6 p-4 bg-opacity-20 bg-gray-700 rounded-lg">
            <h5 class="text-xl font-bold mb-2 text-white">Unentschieden!</h5>
            <p class="text-gray-300">Beide Spieler haben die gleiche Punktzahl erreicht.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
