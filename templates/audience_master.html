{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-2 gap-4">
        <h3 class="text-2xl sm:text-4xl font-bold text-white bg-gradient-to-r from-cyan-500 to-magenta-500 inline-block text-transparent bg-clip-text">Masterfrage - Publikumsteilnahme</h3>
        <button id="refresh-question-button" class="action-button primary text-lg py-3 px-6 font-bold shadow-lg" title="Frage aktualisieren">
            <i class="fas fa-sync-alt mr-2"></i> Aktualisieren
        </button>
    </div>
    <p class="text-xl text-white mb-6">
        Setze deinen Einsatz und beantworte die Masterfrage!
    </p>
</div>

<div class="question-container">
    <div class="question-text" id="question-text" style="background: linear-gradient(135deg, #003b6f, #0077cc);">
        {{ master.question.text }}
    </div>

    {% if master.question.question_image %}
    <div class="question-image mt-6 mb-6">
        <img src="{{ master.question.question_image }}" alt="Fragebild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('{{ master.question.question_image }}')"
             style="max-height: 300px; cursor: pointer;">
    </div>
    {% endif %}

    <div class="mb-8">
        <label class="block text-lg font-medium text-white mb-2">Wähle einen Spieler:</label>
        <div class="flex justify-center space-x-4 mb-4">
            <button id="player-0" class="player-button flex flex-col items-center p-3 rounded-lg border-2 border-transparent hover:border-player1 transition-all">
                <img src="{{ game_state.players[0].image }}" alt="{{ game_state.players[0].name }}" class="w-16 h-16 rounded-full mb-2">
                <span class="text-player1 font-bold">{{ game_state.players[0].name }}</span>
                {% set total_score_0 = game_state.players[0].score + game_state.audience_points[0] %}
                <span class="text-white">{{ total_score_0 }} Punkte</span>
            </button>
            <button id="player-1" class="player-button flex flex-col items-center p-3 rounded-lg border-2 border-transparent hover:border-player2 transition-all">
                <img src="{{ game_state.players[1].image }}" alt="{{ game_state.players[1].name }}" class="w-16 h-16 rounded-full mb-2">
                <span class="text-player2 font-bold">{{ game_state.players[1].name }}</span>
                {% set total_score_1 = game_state.players[1].score + game_state.audience_points[1] %}
                <span class="text-white">{{ total_score_1 }} Punkte</span>
            </button>
        </div>

        <label class="block text-lg font-medium text-white mb-2">Dein Einsatz:</label>
        <div class="flex items-center justify-center">
            <input type="range" id="bet-slider" min="0" max="100" value="0" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
        </div>
        <div class="text-center mt-2">
            <span id="bet-value" class="text-2xl font-bold text-white">0</span> <span class="text-white">Punkte</span>
        </div>
    </div>

    <div class="answers-container">
        {% for answer in master.question.answers %}
        <button class="answer-button animate-slide-in" id="answer-{{ loop.index0 }}" data-index="{{ loop.index0 }}" style="animation-delay: {{ loop.index0 * 0.1 }}s">
            <span class="answer-letter">{{ ['A', 'B', 'C'][loop.index0] }}</span>
            {{ answer }}
        </button>
        {% endfor %}
    </div>

    <div class="flex justify-center mt-8">
        <button class="action-button primary" id="submit-button">Antwort bestätigen</button>
    </div>

    <div id="result-display" class="mt-8 text-center hidden">
        <div id="waiting-message" class="text-lg text-white">
            <p>Deine Antwort wurde gespeichert!</p>
            <p class="mt-2">Warte auf die Auflösung durch den Moderator...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the refresh button
        const refreshQuestionButton = document.getElementById('refresh-question-button');
        if (refreshQuestionButton) {
            refreshQuestionButton.addEventListener('click', function() {
                window.location.reload();
            });
        }

        const betSlider = document.getElementById('bet-slider');
        const betValue = document.getElementById('bet-value');
        const submitButton = document.getElementById('submit-button');
        const playerButtons = document.querySelectorAll('.player-button');
        const answerButtons = document.querySelectorAll('.answer-button');
        const resultDisplay = document.getElementById('result-display');
        const waitingMessage = document.getElementById('waiting-message');

        let selectedPlayer = null;
        let selectedIndex = null;
        let currentBet = 0;
        let hasAnswered = false;

        // Check if we've already answered
        const hasAnsweredMaster = localStorage.getItem('audience_answered_master');
        if (hasAnsweredMaster === 'true') {
            hasAnswered = true;
            waitingMessage.classList.remove('hidden');
            resultDisplay.classList.remove('hidden');

            // Disable all inputs
            betSlider.disabled = true;
            submitButton.disabled = true;
            submitButton.classList.add('opacity-50');

            playerButtons.forEach(btn => {
                btn.disabled = true;
                btn.classList.add('opacity-50');
            });

            answerButtons.forEach(btn => {
                btn.style.pointerEvents = 'none';
                btn.classList.add('opacity-70');
            });

            // Start polling for results
            startPolling();
        }

        // Update bet value when slider changes
        betSlider.addEventListener('input', function() {
            currentBet = parseInt(this.value);
            betValue.textContent = currentBet;
        });

        // Add event listeners to player buttons
        playerButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (!hasAnswered) {
                    // Remove active class from all buttons
                    playerButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('border-player1');
                        btn.classList.remove('border-player2');
                    });

                    // Get player index from button ID
                    const playerId = this.id;
                    selectedPlayer = parseInt(playerId.split('-')[1]);

                    // Add active class to selected button
                    this.classList.add('active');
                    if (selectedPlayer === 0) {
                        this.classList.add('border-player1');
                    } else {
                        this.classList.add('border-player2');
                    }
                }
            });
        });

        // Add event listeners to answer buttons
        answerButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (!hasAnswered) {
                    // Remove active class from all buttons
                    answerButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to selected button
                    this.classList.add('active');
                    this.style.borderColor = '#4f46e5';
                    this.style.backgroundColor = 'rgba(79, 70, 229, 0.1)';

                    // Store selected index
                    selectedIndex = parseInt(this.dataset.index);
                }
            });
        });

        // Add event listener to submit button
        submitButton.addEventListener('click', function() {
            if (hasAnswered) return;

            if (selectedPlayer === null) {
                alert('Bitte wähle einen Spieler aus!');
                return;
            }

            if (selectedIndex === null) {
                alert('Bitte wähle eine Antwort aus!');
                return;
            }

            if (currentBet <= 0) {
                alert('Bitte setze einen Einsatz größer als 0!');
                return;
            }

            // Submit the bet and answer
            submitBetAndAnswer(selectedPlayer, currentBet, selectedIndex);
        });

        function submitBetAndAnswer(playerIndex, bet, answerIndex) {
            fetch('/api/audience-master-bet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    player: playerIndex,
                    bet: bet,
                    answer: answerIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hasAnswered = true;

                    // Store that we've answered
                    localStorage.setItem('audience_answered_master', 'true');

                    // Show waiting message
                    waitingMessage.classList.remove('hidden');
                    resultDisplay.classList.remove('hidden');

                    // Disable all inputs
                    betSlider.disabled = true;
                    submitButton.disabled = true;
                    submitButton.classList.add('opacity-50');

                    playerButtons.forEach(btn => {
                        btn.disabled = true;
                        btn.classList.add('opacity-50');
                    });

                    answerButtons.forEach(btn => {
                        btn.style.pointerEvents = 'none';
                        btn.classList.add('opacity-70');
                    });

                    // Start polling for results
                    startPolling();
                } else {
                    alert('Es gab ein Problem bei der Antwortübermittlung: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting bet and answer:', error);
                alert('Es gab ein Problem bei der Antwortübermittlung. Bitte versuche es erneut.');
            });
        }

        function startPolling() {
            // Poll every 3 seconds to check if the master question has been resolved
            const pollInterval = setInterval(function() {
                fetch('/api/game-state')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const gameState = data.game_state;

                        // Check if the game phase has changed to 'finished'
                        if (gameState.game_phase === 'finished') {
                            clearInterval(pollInterval);

                            // Clear the local storage flag
                            localStorage.removeItem('audience_answered_master');

                            // Redirect to the results page
                            window.location.href = '/audience/results';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error polling game state:', error);
                });
            }, 3000);
        }
    });
</script>
{% endblock %}
