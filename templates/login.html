<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Wer weiß denn sowas? - Hochzeitsedition</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Montserrat', 'Open Sans', 'sans-serif'],
                        'display': ['Montserrat', 'Open Sans', 'sans-serif'],
                    },
                    colors: {
                        'background': '#001428',
                        'primary': '#00b4d8',
                        'secondary': '#0077b6',
                        'accent': '#90e0ef',
                        'player1': '#00f5ff',
                        'player2': '#ff6b6b',
                        'card-bg': '#003b6f',
                        'card-border': '#0077cc',
                    },
                    aspectRatio: {
                        '16/9': '16 / 9',
                    },
                    boxShadow: {
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                    },
                    backdropBlur: {
                        'glass': '4px',
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            background-color: #001428;
            background-image: linear-gradient(135deg, #001428 0%, #003b6f 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Montserrat', 'Open Sans', sans-serif;
            font-weight: bold;
        }

        .action-button {
            background: linear-gradient(135deg, #00b4d8, #0077b6);
            color: white;
            font-weight: bold;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .action-button:hover {
            background: linear-gradient(135deg, #0077b6, #005577);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 180, 216, 0.3);
        }

        .action-button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .action-button.success:hover {
            background: linear-gradient(135deg, #059669, #047857);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .action-button.primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        .action-button.primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .glass-container {
            background: rgba(0, 59, 111, 0.9);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border-radius: 1rem;
            border: 2px solid #0077cc;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="bg-background">
    <div class="min-h-screen flex flex-col justify-center items-center p-6">
        <!-- Publikumsteilnahme Button -->
        <div class="mb-12 text-center animate-fade-in">
            <a href="/qrcode" class="action-button success text-xl px-8 py-4 inline-flex items-center">
                <i class="fas fa-users mr-3 text-2xl"></i> Publikumsteilnahme
            </a>
        </div>

        <!-- Moderator Login -->
        <div class="glass-container p-8 rounded-xl max-w-md w-full animate-fade-in">
            <h2 class="text-3xl font-bold mb-6 text-center text-white">Moderator Login</h2>

            {% if error %}
            <div class="bg-red-500 bg-opacity-30 text-white p-3 rounded-lg mb-6">
                {{ error }}
            </div>
            {% endif %}

            <form method="POST" action="{{ url_for('quiz.login') }}">
                <div class="mb-6">
                    <label for="password" class="block text-white mb-2">Passwort</label>
                    <input type="password" id="password" name="password"
                           class="w-full p-3 rounded-lg bg-black bg-opacity-30 text-white border border-gray-600 focus:border-primary focus:outline-none"
                           required>
                </div>

                <div class="flex justify-center">
                    <button type="submit" class="action-button primary">
                        <i class="fas fa-sign-in-alt mr-2"></i> Anmelden
                    </button>
                </div>
            </form>

            <div class="mt-8 text-center text-white text-sm">
                <p>Nur für Moderatoren. Publikumsteilnehmer können direkt auf die <a href="/audience" class="text-yellow-300 hover:text-yellow-100 hover:underline font-bold">Publikumsseite</a> zugreifen.</p>
            </div>
        </div>
    </div>
</body>
</html>
