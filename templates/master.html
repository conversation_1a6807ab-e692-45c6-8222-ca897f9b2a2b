{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-6">
    <h3 class="text-3xl font-bold mb-4 text-white font-display">Masterfrage</h3>
    <p class="text-lg text-gray-300 mb-6">
        <span class="font-medium text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">{{ current_player.name }}</span>, setze deinen Einsatz und beantworte die Masterfrage!
    </p>
</div>

<div class="question-container">
    <div class="question-text" id="question-text" style="background: linear-gradient(135deg, #be185d, #ec4899);">
        {{ master.question.text }}
    </div>

    {% if master.question.question_image %}
    <div class="question-image mt-6 mb-6">
        <img src="{{ master.question.question_image }}" alt="Fragebild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('{{ master.question.question_image }}')"
             style="max-height: 300px; cursor: pointer;">
    </div>
    {% endif %}

    <div class="mb-8">
        <label class="block text-lg font-medium text-white mb-2">Dein Einsatz:</label>
        <div class="flex items-center justify-center">
            {# Calculate total score including audience points #}
            {% set total_score = current_player.score + game_state.audience_points[game_state.current_player] %}
            {# Ensure player has at least 1 point to bet with #}
            {% if total_score == 0 %}
                {% set total_score = 1 %}
            {% endif %}
            <input type="range" id="bet-slider" min="0" max="{{ total_score }}" value="0" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
        </div>
        <div class="text-center mt-2">
            <span id="bet-value" class="text-2xl font-bold text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">0</span> <span class="text-white">Punkte</span>
        </div>
    </div>

    <div class="answers-container">
        {% for answer in master.question.answers %}
        <button class="answer-button animate-slide-in" id="answer-{{ loop.index0 }}" data-index="{{ loop.index0 }}" style="animation-delay: {{ loop.index0 * 0.1 }}s">
            <span class="answer-letter">{{ ['A', 'B', 'C'][loop.index0] }}</span>
            {{ answer }}
        </button>
        {% endfor %}
    </div>

    <div class="flex justify-center mt-8">
        <button class="action-button primary" id="submit-button">Antwort bestätigen</button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const betSlider = document.getElementById('bet-slider');
        const betValue = document.getElementById('bet-value');
        const answerButtons = document.querySelectorAll('.answer-button');
        const submitButton = document.getElementById('submit-button');

        let selectedIndex = null;
        let currentBet = 0;

        // Update bet value when slider changes
        betSlider.addEventListener('input', function() {
            currentBet = parseInt(this.value);
            betValue.textContent = currentBet;
        });

        // Add event listeners to answer buttons
        answerButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                answerButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to selected button
                this.classList.add('active');
                this.style.borderColor = '#4f46e5';
                this.style.backgroundColor = 'rgba(79, 70, 229, 0.1)';

                // Store selected index
                selectedIndex = parseInt(this.dataset.index);
            });
        });

        // Add event listener to submit button
        submitButton.addEventListener('click', function() {
            if (selectedIndex !== null) {
                // First submit the bet
                submitBet(currentBet).then(() => {
                    // Then submit the answer
                    submitAnswer(selectedIndex);
                });
            } else {
                alert('Bitte wähle eine Antwort aus!');
            }
        });

        function submitBet(bet) {
            return fetch('/api/master-bet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    bet: bet
                })
            })
            .then(response => response.json())
            .catch(error => {
                console.error('Error submitting bet:', error);
            });
        }

        function submitAnswer(answerIndex) {
            fetch('/api/master-answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    answer: answerIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                // Check if game phase is in the response
                if (data.game_state && data.game_state.game_phase === 'finished') {
                    // Go to results page
                    window.location.href = '/results';
                } else {
                    // Go back to master question for next player
                    window.location.href = '/master';
                }
            })
            .catch(error => {
                console.error('Error submitting answer:', error);
            });
        }
    });
</script>
{% endblock %}
