{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-6">
    <h3 class="text-3xl font-bold mb-4 text-white font-display">Masterfrage - Einsatz</h3>
    <p class="text-lg text-gray-300 mb-6">
        <span class="font-medium text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">{{ current_player.name }}</span>, setze deinen Einsatz für die Masterfrage!
    </p>
</div>

<div class="question-container">
    <div class="question-text" id="question-text" style="background: linear-gradient(135deg, #003b6f, #0077cc);">
        {{ master.question.text }}
    </div>

    <div class="mb-8">
        <label class="block text-lg font-medium text-white mb-2">Dein <PERSON>:</label>
        <div class="flex items-center justify-center">
            {# Calculate total score including audience points #}
            {% set total_score = current_player.score + game_state.audience_points[game_state.current_player] %}
            {# Ensure player has at least 1 point to bet with #}
            {% if total_score == 0 %}
                {% set total_score = 1 %}
            {% endif %}
            <input type="range" id="bet-slider" min="0" max="{{ total_score }}" value="0" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
        </div>
        <div class="text-center mt-2">
            <span id="bet-value" class="text-2xl font-bold text-{{ 'player1' if game_state.current_player == 0 else 'player2' }}">0</span> <span class="text-white">Punkte</span>
        </div>
    </div>

    <div class="flex justify-center mt-8">
        <button class="action-button primary" id="submit-button">Einsatz bestätigen</button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const betSlider = document.getElementById('bet-slider');
        const betValue = document.getElementById('bet-value');
        const submitButton = document.getElementById('submit-button');

        let currentBet = 0;

        // Update bet value when slider changes
        betSlider.addEventListener('input', function() {
            currentBet = parseInt(this.value);
            betValue.textContent = currentBet;
        });

        // Add event listener to submit button
        submitButton.addEventListener('click', function() {
            submitBet(currentBet);
        });

        function submitBet(bet) {
            fetch('/api/master-bet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    bet: bet
                })
            })
            .then(response => response.json())
            .then(data => {
                // Redirect to the appropriate page
                window.location.href = '/master';
            })
            .catch(error => {
                console.error('Error submitting bet:', error);
            });
        }
    });
</script>
{% endblock %}
