# Bilder zu Quiz-Fragen hinzufügen

## Übersicht

Sie können jetzt **zwei verschiedene Arten von Bildern** zu Ihren Quiz-Fragen hinzufügen:

1. **<PERSON>agebilder** (`fragebild`) - Werden bei der Frage angezeigt
2. **Antwortbilder** (`antwortbild`) - Werden bei der Erklärung angezeigt

## Schritt-für-Schritt Anleitung

### 1. Bild-Datei hinzufügen

Kopieren Sie Ihre Bild-Datei in den Ordner `static/images/`:

```
static/
  images/
    pokemon-question.jpg  ← Fragebild
    pokedex.png          ← Antwortbild
    hochzeit-foto.jpg    ← Weitere Bilder
    ...
```

### 2. Bilder in der YAML-Datei referenzieren

Öffnen Sie `data/fragen.yaml` und fügen Sie die entsprechenden Felder hinzu:

```yaml
- kategorie: Pokemon
  icon: ring
  text: "Wie<PERSON><PERSON> Pokemons gibt es laut dem offiziellen Nationalen Pokedex?"
  antworten:
    - "900"
    - "1025"
    - "1650"
  korrekt: 1
  fragebild: "/static/images/pokemon-question.jpg"  # Bild bei der Frage
  antwortbild: "/static/images/pokedex.png"        # Bild bei der Erklärung
  erklaerung: "Mit der Veröffentlichung von Pokémon Karmesin und Purpur..."
```

## Wo werden Bilder angezeigt?

### 1. Fragebilder (`fragebild`)
- Werden **direkt unter dem Fragetext** angezeigt
- Sichtbar für Moderator und Publikum
- Funktioniert bei normalen Fragen und der Masterfrage
- **Zweck**: Zur Veranschaulichung der Frage (z.B. "Welches Jahr zeigt dieses Foto?")

### 2. Antwortbilder (`antwortbild`)
- Werden **nach der Antwort-Aufdeckung** angezeigt
- Zusammen mit dem Erklärungstext
- **Zweck**: Zur Erklärung oder Veranschaulichung der Antwort

## Technische Details

### Unterstützte Formate
- **JPG/JPEG** - Empfohlen für Fotos
- **PNG** - Empfohlen für Grafiken mit Transparenz
- **GIF** - Für animierte Bilder

### Empfohlene Bildgrößen
- **Breite**: Maximal 800px
- **Höhe**: Maximal 400px für Fragen, 600px für Erklärungen
- **Dateigröße**: Unter 1MB für schnelle Ladezeiten

### Pfad-Format
Verwenden Sie immer den vollständigen Pfad:
```yaml
fragebild: "/static/images/frage-dateiname.jpg"
antwortbild: "/static/images/antwort-dateiname.jpg"
```

## Funktionen

### Vollbild-Ansicht
- Klicken Sie auf jedes Bild, um es im Vollbild zu betrachten
- Funktioniert auf Desktop und Mobilgeräten

### Responsive Design
- Bilder passen sich automatisch an die Bildschirmgröße an
- Optimiert für Smartphones und Tablets

### Performance
- Bilder werden nur geladen, wenn sie benötigt werden
- Automatische Größenanpassung für bessere Performance

## Beispiele

### 1. Nur Fragebild
```yaml
- kategorie: Hochzeits-essen
  icon: utensils
  text: "In welchem Jahr wurde folgendes Bild von euch aufgenommen?"
  antworten:
    - "2012"
    - "2013"
    - "2014"
  korrekt: 0
  fragebild: "/static/images/hochzeit-foto.jpg"  # Foto wird bei der Frage gezeigt
```

### 2. Nur Antwortbild
```yaml
- kategorie: Lord of the Weed
  icon: ring
  text: "Welches Zitat stammt NICHT aus 'Lord of the Weed'?"
  antworten:
    - "Zitat A"
    - "Zitat B"
    - "Zitat C"
  korrekt: 2
  antwortbild: "/static/images/lord-of-the-weed.jpg"  # Bild wird bei Erklärung gezeigt
  erklaerung: "Das korrekte Zitat stammt aus..."
```

### 3. Sowohl Frage- als auch Antwortbild
```yaml
- kategorie: Pokemon
  icon: ring
  text: "Wieviele Pokemons gibt es laut dem offiziellen Nationalen Pokedex?"
  antworten:
    - "900"
    - "1025"
    - "1650"
  korrekt: 1
  fragebild: "/static/images/pokemon-question.jpg"   # Bild bei der Frage
  antwortbild: "/static/images/pokedex.png"          # Bild bei der Erklärung
  erklaerung: "Mit der Veröffentlichung von Pokémon Karmesin und Purpur..."
```

### 4. Rückwärtskompatibilität (alte Syntax)
```yaml
- kategorie: Alte Frage
  icon: ring
  text: "Eine Frage ohne spezifische Bild-Typen"
  antworten: ["A", "B", "C"]
  korrekt: 0
  bild: "/static/images/altes-bild.jpg"  # Wird automatisch als Antwortbild behandelt
  erklaerung: "Erklärung..."
```

## Tipps

1. **Dateinamen**: Verwenden Sie aussagekräftige Namen ohne Leerzeichen
2. **Bildqualität**: Achten Sie auf gute Bildqualität, aber nicht zu große Dateien
3. **Urheberrecht**: Verwenden Sie nur Bilder, für die Sie die Rechte haben
4. **Testen**: Testen Sie die Anzeige auf verschiedenen Geräten

## Fehlerbehebung

### Bild wird nicht angezeigt
- Überprüfen Sie den Pfad in der YAML-Datei
- Stellen Sie sicher, dass die Datei im `static/images/` Ordner liegt
- Überprüfen Sie die Schreibweise des Dateinamens

### Bild ist zu groß
- Verkleinern Sie das Bild auf maximal 800x400 Pixel
- Komprimieren Sie die Datei, um die Ladezeit zu verbessern

### Bild lädt langsam
- Reduzieren Sie die Dateigröße
- Verwenden Sie JPG für Fotos, PNG für Grafiken
